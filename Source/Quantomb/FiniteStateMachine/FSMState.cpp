// Fill out your copyright notice in the Description page of Project Settings.


#include "Quantomb/FiniteStateMachine/FSMState.h"
#include "Quantomb/FiniteStateMachine/FiniteStateMachine.h"
#include "Quantomb/FiniteStateMachine/FSMAdditiveState.h"
#include "Quantomb/FiniteStateMachine/FSMTransition.h"

void UFSMState::Init_Implementation(UObject* Entity)
{

}

void UFSMState::InitTransitions(UObject* Entity)
{
	for (UFSMTransition* Transition : Transitions)
	{
		Transition->Init(Entity);
		UE_LOG(LogTemp, Display, TEXT("Initialized transition for: %s"), *Transition->ToStateName);
	}

	for (UFSMAdditiveState* AdditiveState : AdditiveStates)
	{
		AdditiveState->Init(Entity);
		UE_LOG(LogTemp, Display, TEXT("Initialized additive state: %s"), *AdditiveState->StateName);
	}
}


void UFSMState::Enter_Implementation(UObject* Entity)
{
}

void UFSMState::Tick_Implementation(UObject* Entity)
{
	// Evaluate additive states
	EvaluateAdditiveStates(Entity);
}

void UFSMState::Exit_Implementation(UObject* Entity)
{
}

void UFSMState::EvaluateTransition_Implementation(UObject* Entity)
{
	for (UFSMTransition* Transition : Transitions)
	{
		if (Transition->GuardFunction(Entity))
		{
			StateMachine->TransitionState(Transition->ToStateName);
		}
	}
}

void UFSMState::EvaluateAdditiveStates_Implementation(UObject* Entity)
{
	for (UFSMAdditiveState* AdditiveState : AdditiveStates)
	{
		const bool bGuardFunction = AdditiveState->GuardFunction(Entity);
		if (!AdditiveState->bIsActive && bGuardFunction)
		{
			StateMachine->ActivateAdditiveState(AdditiveState);
		}
		else if (!bGuardFunction)
		{
			StateMachine->DeactivateAdditiveState(AdditiveState);
		}
	}
}

void UFSMState::AddAdditiveState(UFSMAdditiveState* AdditiveState)
{
	if (AdditiveState && !AdditiveStates.Contains(AdditiveState))
	{
		AdditiveStates.Add(AdditiveState);
	}
}

void UFSMState::RemoveAdditiveState(UFSMAdditiveState* AdditiveState)
{
	if (AdditiveState)
	{
		AdditiveStates.Remove(AdditiveState);

		// If the additive state is active, deactivate it
		if (AdditiveState->bIsActive)
		{
			StateMachine->DeactivateAdditiveState(AdditiveState);
		}
	}
}
