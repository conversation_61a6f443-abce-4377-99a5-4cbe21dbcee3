// Fill out your copyright notice in the Description page of Project Settings.


#include "Quantomb/FiniteStateMachine/FiniteStateMachine.h"
#include "Quantomb/FiniteStateMachine/FSMState.h"
#include "Quantomb/FiniteStateMachine/FSMAdditiveState.h"

// Sets default values for this component's properties
UFiniteStateMachine::UFiniteStateMachine()
{
	// Set this component to be initialized when the game starts, and to be ticked every frame.  You can turn these features
	// off to improve performance if you don't need them.
	PrimaryComponentTick.bCanEverTick = true;
}

// Called when the game starts
void UFiniteStateMachine::BeginPlay()
{
	Super::BeginPlay();

	// The first state in the array will be the default state
	if (sizeof(States) > 0)
	{
		CurrentState = *States.begin();
	}
	ParentObject = Cast<UObject>(GetOwner());

	// Initialize each state by setting a pointer back to this FSM
	// and calling the InitTransitions()
	for (UFSMState* State : States)
	{
		if (State && ParentObject)
		{
			State->StateMachine = this;
			State->Init(ParentObject);
			State->InitTransitions(ParentObject);
			UE_LOG(LogTemp, Display, TEXT("Initialized state with name: %s"), *State->StateName);
		}
	}
}

UFSMState* UFiniteStateMachine::GetCurrentState()
{
	return CurrentState;
}

TArray<UFSMAdditiveState*> UFiniteStateMachine::GetCurrentActiveAdditiveStates()
{
	return ActiveAdditiveStates;
}

UFSMState* UFiniteStateMachine::GetStateByName(const FString& TargetName)
{
	UFSMState** FoundStatePtr = States.FindByPredicate(
		[&TargetName](const UFSMState* State)
		{
			return State && State->StateName.Equals(TargetName, ESearchCase::IgnoreCase);
		}
		);
	return FoundStatePtr ? *FoundStatePtr : nullptr;
}

void UFiniteStateMachine::TransitionState(FString StateName)
{
	UE_LOG(LogTemp, Display, TEXT("Transitioning state: %s"), *StateName);
	if (UFSMState* TargetState = GetStateByName(StateName))
	{
		UE_LOG(LogTemp, Display, TEXT("Found state: %s"), *TargetState->StateName);
		SetNewState(TargetState);
	}
}

void UFiniteStateMachine::SetNewState(UFSMState* NewState)
{
	if (CurrentState)
	{
		// Deactivate all active additive states that are not allowed for the new state
		TArray<UFSMAdditiveState*> StatesToDeactivate = ActiveAdditiveStates.FilterByPredicate(
			[NewState](const UFSMAdditiveState* AdditiveState)
			{
				return !NewState->AdditiveStates.FindByPredicate([AdditiveState](const UFSMAdditiveState* StateToCheck)
				{
					return StateToCheck->StateName == AdditiveState->StateName;
				});
			});
		for (UFSMAdditiveState* AdditiveState : StatesToDeactivate)
		{
			UE_LOG(LogTemp, Display, TEXT("Deactivating AdditiveState: %s"), *AdditiveState->StateName);
			DeactivateAdditiveState(AdditiveState);
		}

		CurrentState->Exit(ParentObject);
	}

	CurrentState = NewState;

	if (CurrentState)
	{
		CurrentState->Enter(ParentObject);
	}
}

// Called every frame to trigger transition evaluation and per tick logic in the current state
void UFiniteStateMachine::TickComponent(float DeltaTime, ELevelTick TickType,
                                        FActorComponentTickFunction* ThisTickFunction)
{
	Super::TickComponent(DeltaTime, TickType, ThisTickFunction);

	// Tick the current state and evaluate transitions
	if (CurrentState && ParentObject)
	{
		CurrentState->EvaluateTransition(ParentObject);
		CurrentState->Tick(ParentObject);
	}

	// Tick all active additive states
	for (UFSMAdditiveState* AdditiveState : ActiveAdditiveStates)
	{
		if (AdditiveState && ParentObject)
		{
			AdditiveState->Tick(ParentObject);
		}
	}
}

void UFiniteStateMachine::ActivateAdditiveState(UFSMAdditiveState* AdditiveState)
{
	// First check if we already have an active state with the same name
	bool bAlreadyActive = ActiveAdditiveStates.ContainsByPredicate(
		[AdditiveState](const UFSMAdditiveState* ExistingState)
		{
			return ExistingState && AdditiveState &&
				ExistingState->StateName.Equals(AdditiveState->StateName, ESearchCase::IgnoreCase);
		});

	if (AdditiveState && !bAlreadyActive)
	{
		// Mark the state as active
		AdditiveState->bIsActive = true;

		// Add to active states array
		ActiveAdditiveStates.Add(AdditiveState);

		// Call Enter on the additive state
		AdditiveState->Enter(ParentObject);

		UE_LOG(LogTemp, Display, TEXT("Activated additive state: %s"), *AdditiveState->StateName);
	}
}

void UFiniteStateMachine::DeactivateAdditiveState(UFSMAdditiveState* AdditiveState)
{
	UFSMAdditiveState** StatePtr = ActiveAdditiveStates.FindByPredicate(
		[AdditiveState](const UFSMAdditiveState* ExistingState)
		{
			return ExistingState && AdditiveState &&
				ExistingState->StateName.Equals(AdditiveState->StateName, ESearchCase::IgnoreCase);
		});

	if (StatePtr && *StatePtr)
	{
		UFSMAdditiveState* State = *StatePtr;
		// Mark the state as inactive
		State->bIsActive = false;

		// Call Exit on the additive state
		State->Exit(ParentObject);

		// Remove from active states array
		ActiveAdditiveStates.Remove(State);

		UE_LOG(LogTemp, Display, TEXT("Deactivated additive state: %s"), *AdditiveState->StateName);
	}
}
