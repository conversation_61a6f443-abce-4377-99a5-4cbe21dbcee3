// Copyright Epic Games, Inc. All Rights Reserved.

#include "../Common.ush"
#include "../Hash.ush"

#include "RayTracingCommon.ush"
#include "RayTracingDebugUtils.ush"
#include "RayTracingHitGroupCommon.ush"

RAY_TRACING_ENTRY_CLOSEST_HIT(RayTracingDebugMainCHS,
	FRayTracingDebugPayload, Payload,
	FRayTracingIntersectionAttributes, Attributes)
{
	Payload.HitT = RayTCurrent();

	const uint GPUSceneInstanceId = GetInstanceUserData();

	const FInstanceSceneData InstanceSceneData = GetInstanceSceneData(GPUSceneInstanceId, Scene.GPUScene.InstanceDataSOAStride);

	Payload.InstanceHash = MurmurAdd(InstanceSceneData.PrimitiveId, InstanceSceneData.RelativeId);

	Payload.TriangleIndex = PrimitiveIndex();

	Payload.WorldNormal = GetGeometryNormalFromTriangleBaseAttributes(PrimitiveIndex());
	Payload.InstanceIndex = InstanceIndex();
	Payload.GeometryIndex = 0; // GeometryIndex();
	Payload.ScenePrimitiveIndex = InstanceSceneData.PrimitiveId;
}

RAY_TRACING_ENTRY_ANY_HIT(RayTracingDebugAHS,
	FRayTracingDebugPayload, Payload,
	FRayTracingIntersectionAttributes, Attributes)
{
	Payload.TriangleHitCountPerRay += 1;
}
